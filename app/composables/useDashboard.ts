import { createSharedComposable } from "@vueuse/core";

const _useDashboard = () => {
  const route = useRoute();
  const isSearchOpen = ref(false);
  const aboutGunsModal = ref(false);
  const authOpenModal = ref(false);
  const categories = [
    {
      category: "hunting",
      registration: true,
      label: "Охотничье оружие",
      image: "/images/cat-1.png",
      colSpan: "col-span-6 md:col-span-4",
      slideColspan: "col-span-6",
      index: 1
    },
    {
      category: "pnevma",
      registration: false,
      label: "Пневматика",
      image: "/images/cat-3.png",
      colSpan: "col-span-6 md:col-span-2",
      slideColspan: "col-span-4",
      index: 6
    },
    {
      category: "optika_priceli",
      registration: false,
      label: "Оптика",
      image: "/images/cat-4.png",
      colSpan: "col-span-4 md:col-span-2",
      slideColspan: "col-span-4",
      index: 7
    },
    {
      category: "knife",
      registration: false,
      label: "Ножи&nbsp;и клинки",
      image: "/images/cat-7.png",
      colSpan: "col-span-5 md:col-span-2",
      slideColspan: "col-span-4",
      index: 5
    },
    {
      category: "zip",
      registration: false,
      label: "ЗИП",
      image: "/images/cat-5.png",
      colSpan: "col-span-3 md:col-span-2",
      slideColspan: "col-span-4",
      index: 3
    },
    {
      category: "selfdefence",
      registration: true,
      label: "Оружие самообороны",
      image: "/images/cat-2.png",
      colSpan: "col-span-5 md:col-span-3",
      slideColspan: "col-span-6",
      index: 2
    },
    {
      category: "safes",
      registration: false,
      label: "Сейфы",
      image: "/images/cat-6.png",
      colSpan: "col-span-3 md:col-span-2",
      slideColspan: "col-span-4",
      index: 4
    },
    {
      category: "kobury",
      registration: false,
      label: "Сумки, Кобуры",
      image: "/images/cat-8.png",
      colSpan: "col-span-4 md:col-span-2",
      slideColspan: "col-span-4",
      index: 8
    },
    {
      category: "reloading",
      registration: false,
      label: "Релоадинг и оборудование",
      image: "/images/cat-9.png",
      colSpan: "col-span-6 md:col-span-3",
      slideColspan: "col-span-6",
      index: 9
    },
    {
      category: "airsoft",
      registration: false,
      label: "Страйкбол",
      image: "/images/cat-10.png",
      colSpan: "col-span-6 md:col-span-2",
      slideColspan: "col-span-6",
      index: 10
    }
  ];

  defineShortcuts({
    meta_k: () => (isSearchOpen.value = !isSearchOpen.value)
  });

  watch(
    () => route.fullPath,
    () => {
      isSearchOpen.value = false;
      authOpenModal.value = false;
    }
  );

  return {
    isSearchOpen,
    aboutGunsModal,
    authOpenModal,
    categories
  };
};

export const useDashboard = createSharedComposable(_useDashboard);
