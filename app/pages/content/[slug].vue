<template>
  <UContainer class="mt-4">
    <div class="flex items-center gap-2">
      <UButton to="/news" size="xs" color="neutral" variant="outline" icon="i-lucide-arrow-left"
        >Назад</UButton
      >
      <NuxtTime
        v-if="news.data?.published_at"
        :datetime="news.data?.published_at"
        date-style="long"
        time-style="short"
        class="text-sm text-[var(--ui-text-muted)]"
      />
    </div>

    <UPageCard :title="news.data?.title" variant="naked" orientation="horizontal" class="mt-3 mb-4">
      <template #title>
        <h1 class="text-2xl font-bold">{{ news.data?.title }}</h1>
      </template>
    </UPageCard>

    <div class="flex flex-col gap-4 lg:flex-row">
      <div class="w-full">
        <NewsPost :news="news" />
      </div>
      <div class="w-full space-y-4 lg:w-110">
        <div class="sticky top-2 space-y-2">
          <div v-for="post in posts.data" :key="post.slug">
            <CardItemSmall :item="post" />
          </div>

          <!--          <UButton to="/news" size="sm" block variant="soft"> Новости </UButton> -->
        </div>
      </div>
    </div>

    <h3 class="my-4 font-semibold text-(--ui-text-highlighted)">Другие новости</h3>
    <div class="grid w-full grid-cols-1 gap-4 md:grid-cols-3">
      <UBlogPost
        v-for="(item, index) in news.other"
        :key="index"
        :to="{ name: 'news-slug', params: { slug: item.slug } }"
        :title="item.title"
        :description="item.description"
        :image="item.cover"
        :date="item.published_at"
      />
    </div>
  </UContainer>
</template>

<script setup lang="ts">
import type { NewsSingleResponse } from "~/types/news";
import type { PostsResponse } from "~/types/listing";

const route = useRoute();
const client = useSanctumClient();

const { data: news } = await useAsyncData<NewsSingleResponse>(`news:${route.params.slug}`, () =>
  client(`/news/${route.params.slug}`)
);

const { data: posts } = await useAsyncData<PostsResponse>("posts:limit:5", () =>
  client("/posts?limit=5")
);

if (!news.value?.data) {
  throw createError({ statusCode: 404, fatal: true });
}

useSchemaOrg([
  defineBreadcrumb({
    itemListElement: [
      { name: "Главная", item: "/" },
      { name: "Новости", item: "/news" },
      { name: news.value.data.title }
    ]
  }),
  defineArticle({
    headline: news.value?.data?.meta_title || news.value?.data?.title,
    description: news.value?.data?.meta_description || news.value?.data?.description,
    image: news.value?.data?.image,
    datePublished: news.value.data?.published_at,
    dateModified: news.value.data?.updated_at,
    author: {
      name: "Паша Прав",
      url: "https://pasha-prav.ru/",
      image: "/images/pasha.jpg"
    }
  })
]);

useSeoMeta({
  title: news.value?.data?.meta_title || news.value?.data?.title,
  ogTitle: news.value?.data?.meta_title || news.value?.data?.title,
  description: news.value?.data?.meta_description || news.value?.data?.description,
  ogDescription: news.value?.data?.meta_description || news.value?.data?.description,
  ogImage: news.value?.data?.image,
  ogType: "article",
  ogLocale: "ru"
});
</script>
