<template>
  <UDashboardPanel class="!min-h-auto">
    <template #header>
      <UDashboardNavbar :toggle="false">
        <template #title>
          <CardItemWidget v-if="chat?.data?.source" :item="chat?.data.source" />
          <div v-else-if="chat?.data?.id === user.support_chat_id" class="flex items-center gap-2">
            <UIcon name="i-lucide-life-buoy" class="text-success-500 size-10" />
            <div>
              <div class="font-medium">Поддердка ГанПост</div>
              <div class="text-md mb-auto text-[var(--ui-text-muted)] md:text-sm">
                Официальный канал связи с администрацией
              </div>
            </div>
          </div>
        </template>

        <template #leading>
          <UButton icon="i-lucide-x" color="neutral" variant="ghost" class="-ms-1.5" to="/inbox" />
        </template>

        <template v-if="false" #right>
          <UTooltip text="Archive">
            <UButton icon="i-lucide-inbox" color="neutral" variant="ghost" />
          </UTooltip>

          <UTooltip text="Reply">
            <UButton icon="i-lucide-reply" color="neutral" variant="ghost" />
          </UTooltip>

          <!-- <UDropdownMenu :items="dropdownItems">
                   <UButton
                       icon="i-lucide-ellipsis-vertical"
                       color="neutral"
                       variant="ghost"
                   />
                 </UDropdownMenu> -->
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div
        :class="chat?.data?.messages?.length ? 'h-[calc(100vh-260px)]' : 'h-[calc(100vh-280px)]'"
      >
        <UChatMessages
          :user="{
            side: 'right',
            variant: 'solid'
          }"
          :assistant="{
            side: 'left',
            variant: 'soft'
          }"
          should-auto-scroll
          :messages="messages as Message[]"
          :status="chatMessagesStatus"
        >
          <template #leading="{ message }: { message: Message }">
            <UAvatar v-if="message?.user?.avatar" v-bind="message.user.avatar" />
          </template>
          <template #content="{ message }: { message: Message }">
            <div>{{ message.content }}</div>
            <div v-if="message.media?.length" class="message-gallery mt-2 flex flex-wrap gap-2">
              <div v-for="image in message.media" :key="image.src" class="relative block h-14 w-14">
                <a
                  class="relative block h-14 w-14"
                  :href="image.src"
                  :data-pswp-width="image?.width"
                  :data-pswp-height="image?.height"
                >
                  <img
                    alt="gunpost.ru"
                    :src="image.src"
                    class="z-0 h-full w-full rounded-sm object-cover"
                  />
                </a>
              </div>
            </div>
          </template>
          <template #actions="{ message }: { message: Message }">
            <NuxtTime
              v-if="message?.created_at"
              :datetime="message.created_at"
              date-style="long"
              time-style="short"
              class="mb-2 text-xs"
            />
          </template>
        </UChatMessages>
      </div>
    </template>
    <template #footer>
      <UContainer>
        <UChatPrompt
          v-model="input"
          placeholder="Ваше сообщение..."
          :rows="2"
          :loading="isLoading"
          :error="error"
          @input="typingMessage"
          @submit="handleSubmit"
        >
          <template #footer>
            <div
              v-show="images?.length"
              class="input-gallery flex w-full flex-wrap gap-2 border-t border-[var(--ui-border)] pt-1"
            >
              <div v-for="image in images" :key="image.src" class="relative block h-14 w-14">
                <a
                  class="relative block h-14 w-14"
                  :href="image.src"
                  :data-pswp-width="image?.width"
                  :data-pswp-height="image?.height"
                >
                  <img
                    alt="gunpost.ru"
                    :src="image.src"
                    class="z-0 h-full w-full rounded-sm object-cover"
                  />
                </a>
                <UButton
                  icon="i-lucide-x"
                  size="xs"
                  color="error"
                  class="absolute top-1 right-1 z-10"
                  @click.prevent="removeImage(image)"
                />
              </div>
            </div>
          </template>
          <div class="flex gap-1">
            <input
              ref="fileRef"
              type="file"
              multiple
              class="hidden"
              accept=".jpg, .jpeg, .png"
              @change="onFileChange"
            />
            <UButton icon="i-lucide-image-plus" variant="outline" square @click="onFileClick" />
            <UChatPromptSubmit icon="i-lucide-send" variant="solid" :status="promptSendingStatus" />
          </div>
        </UChatPrompt>
      </UContainer>
    </template>
  </UDashboardPanel>
</template>

<script setup lang="ts">
import PhotoSwipeLightbox from "photoswipe/lightbox";
import "photoswipe/style.css";

import { onMounted, onUnmounted } from "vue";
import type { User } from "~/types/auth";
import type { Chat, ChatImage, ChatUser, Message } from "~/types/chat";

definePageMeta({
  layout: "dashboard",
  middleware: ["sanctum:auth"]
});

const { refreshIdentity } = useSanctumAuth();
const echo = useEcho();
const user = useSanctumUser<User>();
const route = useRoute();
const client = useSanctumClient();
const input = ref("");
const chatMessagesStatus = ref<"error" | "submitted" | "streaming" | "ready">("ready");
const promptSendingStatus = ref<"error" | "submitted" | "streaming" | "ready">("ready");
const fileRef = ref<HTMLInputElement>();
const isLoading = ref(false);
const typing = ref(null);
const images = ref<ChatImage[]>([]);
const lightbox = ref(null);
let typingTimeout = null;
let unreadMessages = false;

const { data: chat, error } = await useAsyncData<{ data: Chat }>(`chats.${route.params.id}`, () =>
  client(`/chats/${route.params.id}`)
);

if (!chat.value) {
  throw createError({ statusCode: 404, fatal: true });
}

await refreshIdentity();

const messages = computed<Message[]>(() =>
  chat.value.data.messages.map((m) => ({
    ...m,
    role: m.user?.id === user.value.id ? "user" : ("assistant" as "user" | "assistant"),
    createdAt: new Date(m.created_at)
  }))
);

/**
 * Открывает окно выбора файла.
 */
function onFileClick() {
  fileRef.value?.click();
}

async function onFileChange(e: Event) {
  const input = e.target as HTMLInputElement;

  if (!input.files?.length) {
    return;
  }

  const files = Array.from(input.files);

  for (const file of files) {
    const src = URL.createObjectURL(file);

    const dimensions = await new Promise<{ width: number; height: number }>((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.src = src;
    });

    images.value.push({
      file,
      src,
      width: dimensions.width,
      height: dimensions.height
    });
  }
}

const removeImage = async (file) => {
  images.value = images.value.filter((image) => image.src !== file.src);
};

const handleSubmit = async () => {
  // "error" | "submitted" | "streaming" | "ready"

  isLoading.value = true;
  promptSendingStatus.value = "submitted";
  // const files = Array.from(input.files);
  const formData = new FormData();
  formData.append("message", input.value);

  for (const img of images.value) {
    formData.append("photos[]", img.file);
  }

  try {
    await client(`/chats/${route.params.id}/message`, {
      method: "POST",
      body: formData
    });

    input.value = "";
    images.value = [];
  } catch (error) {
    console.error(error);
    promptSendingStatus.value = "error";
  }

  await nextTick(() => {
    promptSendingStatus.value = "ready";
    isLoading.value = false;
  });
};

const typingMessage = useDebounceFn(async () => {
  echo.private(`chat.${route.params.id}`).whisper("typing", {
    name: (user.value as unknown as ChatUser)?.name
  });
}, 500);

/**
 * Инициализирует или обновляет lightbox
 */
const initLightbox = () => {
  // Если lightbox уже существует, уничтожаем его перед пересозданием
  if (lightbox.value) {
    lightbox.value.destroy();
  }

  lightbox.value = new PhotoSwipeLightbox({
    gallery: ".message-gallery, .input-gallery",
    children: "a",
    pswpModule: () => import("photoswipe")
  });

  lightbox.value.init();
};

const writeNewMessage = async (e: { message: Message }) => {
  console.log("writeNewMessage", e.message);
  unref(chat).data.messages.push(e.message);
  chat.value = { ...chat.value };

  // Обновляем lightbox только после добавления нового сообщения с изображениями
  if (e.message.media?.length) {
    await nextTick();
    initLightbox();
  }
};

const writeNewTyping = (e: { name: string }) => {
  typing.value = e.name;
  chatMessagesStatus.value = "submitted";

  // Если уже есть таймер, сбрасываем его
  if (typingTimeout) {
    clearTimeout(typingTimeout);
  }

  // Устанавливаем новый таймер на 500 мс для сброса
  typingTimeout = setTimeout(() => {
    typing.value = null;
    chatMessagesStatus.value = "ready";
  }, 600);
};

const handleVisibilityChange = async () => {
  if (document.visibilityState === "visible" && unreadMessages) {
    await client(`/chats/${route.params.id}/read`, {
      method: "POST"
    });
    unreadMessages = false;
  }
};

onMounted(() => {
  echo
    .private(`chat.${route.params.id}`)
    .listen(".message.created", async (e: { message: Message }) => {
      await writeNewMessage(e);
      if (document.visibilityState !== "visible") {
        unreadMessages = true;
      } else {
        await client(`/chats/${route.params.id}/read`, {
          method: "POST"
        });
      }
    })
    .listenForWhisper("typing", (e: { name: string }) => writeNewTyping(e))
    .error((e: object) => {
      console.error("Private channel error", e);
    });

  document.addEventListener("visibilitychange", handleVisibilityChange);

  // Инициализируем lightbox при монтировании компонента
  initLightbox();
});

onUnmounted(() => {
  echo.leaveAllChannels();
  document.removeEventListener("visibilitychange", handleVisibilityChange);

  // Корректно уничтожаем lightbox при размонтировании компонента
  if (lightbox.value) {
    lightbox.value.destroy();
    lightbox.value = null;
  }
});
</script>
