<template>
  <UContainer v-if="posts" class="mt-3">
    <div class="flex flex-col gap-4 lg:flex-row">
      <div class="w-full">
        <div class="space-y-2">
          <UBreadcrumb :items="breadcrumbs" />
          <FiltersCategories v-if="!posts?.city" />

          <div v-if="posts.filters?.types?.values?.length" class="flex flex-wrap gap-1">
            <UButton
              size="xs"
              :to="{ params: { type: '' }, query: route.query }"
              :color="route.params?.type ? 'neutral' : 'primary'"
              variant="soft"
            >
              Все
            </UButton>
            <UButton
              v-for="type in posts.filters.types?.values"
              :key="type.value"
              size="xs"
              :to="getTypeTo(type)"
              :color="route.params?.type === type.value ? 'primary' : 'neutral'"
              variant="soft"
            >
              {{ type.label }}
            </UButton>
          </div>

          <div class="my-2">
            <h1 class="mb-1 text-xl md:text-2xl leading-6 font-bold">
              {{ h1 }}
            </h1>

            <p class="text-base">
              <template v-if="posts.seo?.content">
                {{ posts.seo.content }}
              </template>
              <template v-else>
                {{ pageDescription }}
              </template>
            </p>
          </div>
          <AlertsBase />
        </div>

        <div class="sticky top-0 z-10">
          <div class="flex gap-2 bg-white/80 py-2 backdrop-blur-md dark:bg-zinc-900/95">
            <div class="flex flex-wrap gap-2">
              <FiltersList :filters="posts.filters" />
            </div>
          </div>
        </div>

        <div
          v-if="posts.meta.total > 0"
          class="flex flex-col justify-between gap-1 md:flex-row md:items-center"
        >
          <p class="md:text-md text-sm font-semibold">
            Объявлений в разделе: {{ posts.meta.total }}
          </p>
          <FiltersOrderBy />
        </div>

        <div
          v-if="(!posts?.data?.length || posts.is_empty) && status === 'success'"
          class="mt-4 mb-8"
        >
          <div class="flex flex-col items-center">
            <img class="w-18" alt="gunpost.ru" src="/images/error.png" />
            <p class="my-2 font-bold">Нет подходящих предложений</p>
            <p v-if="posts?.data?.length">Подобрали для вас предложения из соседних городов</p>
            <p v-else>Смягчите фильтры, чтобы увидеть больше.</p>
          </div>
        </div>

        <div class="mt-2 grid w-full grid-cols-1 gap-4 md:grid-cols-3">
          <template v-if="status === 'pending'">
            <Skeleton v-for="i in 21" :key="i" />
          </template>
          <template v-else>
            <CardItem v-for="(item, n) in posts?.data" :key="n" :position="n" :item="item" />
          </template>
        </div>

        <div v-if="posts?.other?.length" class="mt-4">
          <h3 class="text-xl font-semibold text-(--ui-text-highlighted)">Похожие объявления</h3>
          <p>Подобрали для вас предложения из соседних городов</p>

          <div class="mt-2 grid w-full grid-cols-1 gap-4 md:grid-cols-3">
            <CardItem v-for="(item, n) in posts?.other" :key="n" :position="n" :item="item" />
          </div>
        </div>

        <Pagination
          :meta="posts.meta"
          :page="page"
          :disabled="status === 'pending'"
          @set-page="setPage"
        />
      </div>
      <div class="w-full space-y-2 lg:w-110">
        <NewsWidget />
        <div v-if="false" class="space-y-2">
          <h3 class="text-base font-semibold text-pretty text-(--ui-text-highlighted)">
            Популярные обсуждения
          </h3>
          <ULink
            to="/"
            class="block space-y-2 rounded-xl bg-gray-50 p-4 text-left text-sm hover:bg-gray-100 dark:bg-zinc-800 dark:hover:bg-zinc-700"
          >
            <h3 class="leading-none text-black dark:text-white">
              Вопрос по эволюции патронов кольцевого воспламенения
            </h3>
            <p class="line-clamp-3 text-[var(--ui-text-muted)]">
              Поиск по форуму нашел, что в ранних патронах Флобера не было выраженной закраины, а
              только расширение, чтобы патрон не проваливался в ствол и было чего сминать курку
            </p>
            <UUser
              size="3xs"
              name="Ротмистр Чачу"
              description="28-6-2024 12:38"
              :avatar="{ src: 'https://i.pravatar.cc/50?u=Ротмистр Чачу' }"
            />
          </ULink>
          <ULink
            to="/"
            class="block space-y-2 rounded-xl bg-gray-50 p-4 text-left text-sm hover:bg-gray-100 dark:bg-zinc-800 dark:hover:bg-zinc-700"
          >
            <h3 class="leading-none text-black dark:text-white">
              Перествол ВПО-209 366ТКМ в СКС 7, 62
            </h3>
            <p class="line-clamp-3 text-[var(--ui-text-muted)]">
              Имею такой балласт в виде ВПО-209 который в свое время взял для стажа. В этой связи
              вопрос - возможна ли его легальная модернизация до
            </p>
            <UUser
              size="3xs"
              name="jacker2000"
              description="3-2-2025 12:03"
              :avatar="{ src: 'https://i.pravatar.cc/50?u=jacker2000' }"
            />
          </ULink>

          <UButton size="sm" block variant="soft"> Обсуждения </UButton>
        </div>

        <AdsVip v-if="posts?.vipAds?.length" :items="posts.vipAds" />
      </div>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
import type { PostsResponse, NuxtErrorWithType } from "~/types/listing";

const client = useSanctumClient();
const route = useRoute();
const page = ref(route.query.page || 1);

async function getRouteCacheKeyBase64() {
  const path = route.path;
  const query = new URLSearchParams(route.query).toString();
  const rawKey = `${path}?${query}:${page.value}`;
  const buffer = await crypto.subtle.digest("SHA-256", new TextEncoder().encode(rawKey));
  return btoa(String.fromCharCode(...new Uint8Array(buffer)));
}

function cleanParams(params: Record<string, any>): Record<string, any> {
  return Object.fromEntries(
    Object.entries(params).filter(
      ([, value]) =>
        value !== null && value !== undefined && (typeof value !== "string" || value.trim() !== "")
    )
  );
}

const { data: posts, status } = await useAsyncData<PostsResponse>(
  await getRouteCacheKeyBase64(),
  () =>
    client("/posts", {
      params: cleanParams({
        category: route.params?.category,
        city: route.params?.city,
        types: route.params?.type,
        page: page.value,
        path: route.path,
        ...route.query
      })
    }),
  {
    watch: [route]
  }
);

const { title, description, ogTitle, ogDescription, pageDescription, h1 } = useSeoFromFilters(
  posts,
  route
);

useSeoMeta({
  title,
  ogTitle,
  description,
  ogDescription
});

function setPage(value: number) {
  page.value = value;
}

function getTypeTo(type: { label: string; value: string }) {
  delete route.query.page;
  return {
    params: {
      ...route.params,
      type: type.value
    },
    query: route.query
  };
}

if (route.params.category && !posts.value?.category) {
  throw createError({
    statusCode: 404,
    fatal: true,
    type: "category"
  } as NuxtErrorWithType);
}

if (route.params.category === "hunting" && route.params.type && !posts.value?.filters?.gun_types) {
  throw createError({
    statusCode: 404,
    fatal: true,
    type: "type"
  } as NuxtErrorWithType);
}

if (route.params.category !== "hunting" && route.params.type && !posts.value?.filters?.types) {
  throw createError({
    statusCode: 404,
    fatal: true,
    type: "type"
  } as NuxtErrorWithType);
}

const breadcrumbs = ref([
  {
    label: "Главная",
    to: "/"
  }
]);

if (posts.value?.category?.name && posts.value?.city?.value) {
  breadcrumbs.value.push({
    label: posts.value.category.name,
    to: `/${posts.value.category.slug}`
  });
  breadcrumbs.value.push({
    label: posts.value.city.name,
    to: `/${posts.value.city.value}/${posts.value.category.slug}`
  });
} else if (posts.value?.category?.name) {
  breadcrumbs.value.push({
    label: posts.value.category.name,
    to: `/${posts.value.category.slug}`
  });
  breadcrumbs.value.push({
    label: "Вся Россия",
    to: `/${posts.value.category.slug}`
  });
}

if (posts.value?.filters?.types && posts.value?.city?.value && route.params.type) {
  const current = posts.value.filters.types.values.find((v) => v.value === route.params.type);
  breadcrumbs.value.push({
    label: current.label,
    to: `/${posts.value.city.value}/${posts.value.category.slug}/${current.value}`
  });
} else if (posts.value?.filters?.types && route.params.type) {
  const current = posts.value.filters.types.values.find((v) => v.value === route.params.type);
  breadcrumbs.value.push({
    label: current.label,
    to: `/${posts.value.category.slug}/${current.value}`
  });
}
</script>
