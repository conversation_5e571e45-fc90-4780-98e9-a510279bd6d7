<template>
  <div class="w-full">
    <UCarousel
      v-if="$device?.isMobile"
      :items="slides"
      align="end"
      class="animate-peek w-full"
      :ui="{
        item: 'basis-[95%] ps-2'
      }"
    >
      <template #default="{ item: slideItems }">
        <div class="grid grid-cols-12 grid-rows-2 gap-2">
          <UButton
            v-for="item in slideItems"
            :key="item.category"
            :to="{
              name: cityStore?.routeSlug ? 'city-category' : 'category',
              params: { category: item.category, city: cityStore?.routeSlug }
            }"
            :color="route.params.category === item.category ? 'primary' : 'neutral'"
            variant="soft"
            class="slider__item relative block h-14 p-1.5 pl-3.5 md:h-18"
            :class="[item.slideColspan]"
          >
            <h3 class="pr-8 text-sm leading-tight">
              {{ item.label }}
            </h3>
            <NuxtImg
              class="absolute right-0 bottom-0 z-10 h-full pt-4"
              aria-hidden="true"
              :src="item.image"
              :alt="`Иконка категории ${item.label}`"
            />
          </UButton>
        </div>
      </template>
    </UCarousel>
    <div v-else class="grid w-full grid-cols-12 grid-rows-2 gap-2">
      <UButton
        v-for="item in categories"
        :key="item.category"
        :to="{
          name: cityStore?.routeSlug ? 'city-category' : 'category',
          params: { category: item.category, city: cityStore?.routeSlug }
        }"
        :color="route.params.category === item.category ? 'primary' : 'neutral'"
        variant="soft"
        class="relative block h-18 p-2.5 pl-3.5 ring-gray-200 hover:ring-1 dark:ring-gray-800"
        :class="[item.colSpan]"
      >
        <h3 class="pr-12 text-sm leading-tight">
          {{ item.label }}
        </h3>
        <NuxtImg
          class="absolute right-0 bottom-0 z-10 h-full pt-4"
          :alt="`Иконка категории ${item.label}`"
          :src="item.image"
        />
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRoute } from "vue-router";

const { $device } = useNuxtApp();
const cityStore = useCityStore();
const route = useRoute();
const { categories } = useDashboard();

const slides = computed(() => {
  const chunkSize = 5;
  const sortedCategories = [...categories].sort((a, b) => a.index - b.index);
  const result: (typeof categories)[] = [];
  for (let i = 0; i < sortedCategories.length; i += chunkSize) {
    result.push(sortedCategories.slice(i, i + chunkSize));
  }
  return result;
});
</script>

<style scoped>
@keyframes peek {
  0% {
    transform: translateX(0);
  }
  30% {
    transform: translateX(-20%);
  }
  60% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(0);
  }
}

/* применяем анимацию к внутреннему треку карусели */
.animate-peek .grid {
  animation: peek 2s ease-in-out;
}
</style>
